import { Button, Container, Label, NumericInput } from 'pcui';
import { Vec3 } from 'playcanvas';
import { Pose } from '../camera-poses';
import { Events } from '../events';
import { Tooltips } from './tooltips';

class CameraPosePanel extends Container {
    private positionInputs: { x: NumericInput, y: NumericInput, z: NumericInput };
    private targetInputs: { x: NumericInput, y: NumericInput, z: NumericInput };
    private currentFrame: number = 0;
    private isUpdatingFromCamera: boolean = false;
    private events: Events;
    private updateFramePoseButton: Button;
    private initPoses: Pose[];
    private resetPoseButton: Button;

    constructor(events: Events, tooltips: Tooltips, args = {}) {
        args = {
            ...args,
            id: 'camera-pose-panel',
            class: 'camera-pose-panel'
        };

        super(args);

        this.events = events;

        // Title
        // const title = new Label({
        //     text: 'Camera Pose',
        //     class: 'camera-pose-title'
        // });
        // this.append(title);

        const poseSection = new Container({
            class: 'camera-pose-section'
        })

        // Position section
        const positionLabel = new Label({
            text: 'Position:',
            class: 'camera-pose-label'
        });

        const positionContainer = new Container({
            class: 'camera-pose-inputs'
        });

        this.positionInputs = {
            x: new NumericInput({
                placeholder: 'X',
                precision: 3,
                step: 0.1,
                class: 'camera-pose-input'
            }),
            y: new NumericInput({
                placeholder: 'Y',
                precision: 3,
                step: 0.1,
                class: 'camera-pose-input'
            }),
            z: new NumericInput({
                placeholder: 'Z',
                precision: 3,
                step: 0.1,
                class: 'camera-pose-input'
            })
        };

        positionContainer.append(this.positionInputs.x);
        positionContainer.append(this.positionInputs.y);
        positionContainer.append(this.positionInputs.z);
        poseSection.append(positionLabel);
        poseSection.append(positionContainer);

        // Target section
        const targetLabel = new Label({
            text: 'Target:',
            class: 'camera-pose-label'
        });

        const targetContainer = new Container({
            class: 'camera-pose-inputs'
        });

        this.targetInputs = {
            x: new NumericInput({
                placeholder: 'X',
                precision: 3,
                step: 0.1,
                class: 'camera-pose-input'
            }),
            y: new NumericInput({
                placeholder: 'Y',
                precision: 3,
                step: 0.1,
                class: 'camera-pose-input'
            }),
            z: new NumericInput({
                placeholder: 'Z',
                precision: 3,
                step: 0.1,
                class: 'camera-pose-input'
            })
        };

        targetContainer.append(this.targetInputs.x);
        targetContainer.append(this.targetInputs.y);
        targetContainer.append(this.targetInputs.z);
        poseSection.append(targetLabel);
        poseSection.append(targetContainer);
        this.append(poseSection);

        // Action buttons
        const buttonContainer = new Container({
            class: 'camera-pose-buttons'
        });

        this.updateFramePoseButton = new Button({
            text: '更新当前帧',
            class: 'camera-pose-button'
        })

        // this.resetPoseButton = new Button({
        //     text: 'Reset',
        //     class: 'camera-pose-button'
        // })

        // const updateFromCameraButton = new Button({
        //     text: 'Update from Camera',
        //     class: 'camera-pose-button'
        // });

        // const applyToCameraButton = new Button({
        //     text: 'Apply to Camera',
        //     class: 'camera-pose-button'
        // });


        buttonContainer.append(this.updateFramePoseButton);
        // buttonContainer.append(this.resetPoseButton);
        // buttonContainer.append(updateFromCameraButton);
        // buttonContainer.append(applyToCameraButton);
        this.append(buttonContainer);

        // // Event handlers for input changes
        // const onInputChange = () => {
        //     if (!this.isUpdatingFromCamera) {
        //         this.updatePoseFromInputs();
        //     }
        // };

        // Object.values(this.positionInputs).forEach(input => {
        //     input.on('change', onInputChange);
        // });

        // Object.values(this.targetInputs).forEach(input => {
        //     input.on('change', onInputChange);
        // });

        this.updateFramePoseButton.on('click', () => {
            this.updatePoseFromInputs();
        });

        // // Button event handlers
        // updateFromCameraButton.on('click', () => {
        //     this.updateFromCurrentCamera();
        // });

        // applyToCameraButton.on('click', () => {
        //     this.applyToCamera();
        // });

        // Listen to timeline setFrame events (拖动/直接点击时间轴触发)
        this.events.on('timeline.setFrame', (frame: number) => {
            this.currentFrame = frame;
            // Update immediately to show the correct pose
            this.updateFromCurrentPose();
        });

        // Listen to timeline.time events (for animation playback)
        this.events.on('timeline.time', (time: number) => {
            this.currentFrame = Math.floor(time);
            // Update during animation playback
            setTimeout(() => {
                this.updateFromCurrentPose();
            }, 5);
        });

        // 监听场景中相机交互 - 使用节流避免过度更新
        let cameraUpdateTimeout: any = null;
        this.events.on('camera.controller', (eventType: string) => {
            // 避免在位姿面板更新时触发循环
            if (this.isUpdatingFromCamera) {
                return;
            }

            // 对于pointermove事件，使用更长的延迟和节流
            const shouldUpdate = eventType !== 'pointermove' || !this.hasKeyframeAtCurrentFrame();

            if (shouldUpdate) {
                // 清除之前的超时
                if (cameraUpdateTimeout) {
                    clearTimeout(cameraUpdateTimeout);
                }

                // 根据事件类型设置不同的延迟
                const delay = eventType === 'pointermove' ? 200 : 100;

                cameraUpdateTimeout = setTimeout(() => {
                    this.updateFromCurrentCamera();
                    cameraUpdateTimeout = null;
                }, delay);
            }
        });

        // Initialize with current frame
        this.currentFrame = this.events.invoke('timeline.frame') || 0;
        const currentPoses = this.events.invoke('camera.poses');
        this.initPoses = [...currentPoses];
        this.updateFromCurrentPose();
        // this.resetPoseButton.on('click', () => {
        //     this.resetPose();
        // });
    }

    private updateFromCurrentCamera() {
        // Get the current camera pose from the scene
        // This will be either:
        // 1. The exact keyframe pose if cursor is on a keyframe
        // 2. The interpolated pose if cursor is between keyframes
        // 3. The user-controlled camera pose if no animation is active
        this.isUpdatingFromCamera = true;

        // Get the most up-to-date camera pose
        const pose = this.events.invoke('camera.getPose');

        // Round to avoid floating point precision issues
        this.positionInputs.x.value = Math.round(pose.position.x * 1000) / 1000;
        this.positionInputs.y.value = Math.round(pose.position.y * 1000) / 1000;
        this.positionInputs.z.value = Math.round(pose.position.z * 1000) / 1000;

        this.targetInputs.x.value = Math.round(pose.target.x * 1000) / 1000;
        this.targetInputs.y.value = Math.round(pose.target.y * 1000) / 1000;
        this.targetInputs.z.value = Math.round(pose.target.z * 1000) / 1000;

        this.isUpdatingFromCamera = false;
    }

    private updateFromCurrentPose() {
        const poses = this.events.invoke('camera.poses') || [];
        const currentPose = poses.find((pose: any) => pose.frame === this.currentFrame);

        if (currentPose) {
            // Found exact keyframe at current frame - use exact keyframe data
            this.isUpdatingFromCamera = true;
            this.toggleInputs(true);

            // Use exact keyframe values without rounding
            this.positionInputs.x.value = currentPose.position.x;
            this.positionInputs.y.value = currentPose.position.y;
            this.positionInputs.z.value = currentPose.position.z;

            this.targetInputs.x.value = currentPose.target.x;
            this.targetInputs.y.value = currentPose.target.y;
            this.targetInputs.z.value = currentPose.target.z;

            this.isUpdatingFromCamera = false;
        } else {
            // No exact keyframe at current frame
            // Show the interpolated camera pose (what the camera should be at this frame)
            this.toggleInputs(false);
            this.updateFromCurrentCamera();
        }
    }

    private hasKeyframeAtCurrentFrame(): boolean {
        const poses = this.events.invoke('camera.poses') || [];
        return poses.some((pose: any) => pose.frame === this.currentFrame);
    }

    private toggleInputs(enabled: boolean) {
        Object.values(this.positionInputs).forEach(input => {
            input.enabled = enabled;
        });

        Object.values(this.targetInputs).forEach(input => {
            input.enabled = enabled;
        });

        this.updateFramePoseButton.enabled = enabled;
        // this.resetPoseButton.enabled = enabled;
    }

    private resetPose() {
        const initPose = this.initPoses.find((pose: any) => pose.frame === this.currentFrame);
        console.log('initPose: '+JSON.stringify(initPose));
        if (initPose) {
            this.events.fire('camera.updatePose', this.currentFrame, initPose.position, initPose.target);
        }
    }

    private updatePoseFromInputs() {
        const position = new Vec3(
            this.positionInputs.x.value || 0,
            this.positionInputs.y.value || 0,
            this.positionInputs.z.value || 0
        );

        const target = new Vec3(
            this.targetInputs.x.value || 0,
            this.targetInputs.y.value || 0,
            this.targetInputs.z.value || 0
        );

        // Check if there's already a pose at current frame
        const poses = this.events.invoke('camera.poses') || [];
        const existingPoseIndex = poses.findIndex((pose: any) => pose.frame === this.currentFrame);

        if (existingPoseIndex !== -1) {
            // Update existing pose using the new event
            this.events.fire('camera.updatePose', this.currentFrame, position, target);
        } else {
            // Create new pose
            this.events.fire('camera.addPose', {
                name: `camera_${poses.length}`,
                frame: this.currentFrame,
                position: position,
                target: target
            });
        }
    }

    private applyToCamera() {
        const position = new Vec3(
            this.positionInputs.x.value || 0,
            this.positionInputs.y.value || 0,
            this.positionInputs.z.value || 0
        );

        const target = new Vec3(
            this.targetInputs.x.value || 0,
            this.targetInputs.y.value || 0,
            this.targetInputs.z.value || 0
        );

        this.events.fire('camera.setPose', { position, target }, 0);
    }
}

export { CameraPosePanel };
